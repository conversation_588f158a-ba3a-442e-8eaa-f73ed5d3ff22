<!-- pages/delivery/delivery.wxml -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>

  <!-- 主要内容 -->
  <block wx:else>
    <!-- 配送信息区域 -->
    <view class="delivery-info">
      <!-- 配送地址 -->
      <view class="info-item" bindtap="showAddressSelector">
        <view class="info-label">配送地址</view>
        <view class="info-content">
          <view class="address-info" wx:if="{{deliveryAddress}}">
            <text class="address-name">{{deliveryAddress.name}} {{deliveryAddress.phone}}</text>
            <text class="address-detail">{{deliveryAddress.province}}{{deliveryAddress.city}}{{deliveryAddress.district}}{{deliveryAddress.detail}}</text>
          </view>
          <text class="info-placeholder" wx:else>请选择配送地址</text>
          <text class="info-arrow">></text>
        </view>
      </view>

      <!-- 配送时间 -->
      <view class="info-item" bindtap="showTimeSelector">
        <view class="info-label">配送时间</view>
        <view class="info-content">
          <text class="info-text" wx:if="{{selectedTimeSlot}}">{{selectedTimeSlot.label}}</text>
          <text class="info-placeholder" wx:else>请选择配送时间</text>
          <text class="info-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 左右分栏布局 -->
    <view class="main-layout">
      <!-- 左侧分类栏 -->
      <view class="left-sidebar">
        <scroll-view scroll-y="true" class="categories-scroll">
          <view class="category-item {{currentCategory === item.id ? 'active' : ''}}"
                wx:for="{{categories}}"
                wx:key="id"
                bindtap="selectCategory"
                data-id="{{item.id}}">
            <text class="category-text">{{item.name}}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧商品列表 -->
      <view class="right-products">
        <scroll-view scroll-y="true" class="product-list" bindscrolltolower="onReachBottom">
          <view class="product-category-title" wx:if="{{currentCategoryDishes.length > 0 && categories[currentCategory]}}">
            {{categories[currentCategory].name}}
          </view>

          <view class="product-item" wx:for="{{currentCategoryDishes}}" wx:key="id">
            <image class="product-image"
                   src="{{item.image || 'https://vegan.yiheship.com/static/images/default-dish.png'}}"
                   mode="aspectFill"
                   bindtap="showDishDetail"
                   data-dish="{{item}}">
            </image>
            <view class="product-info">
              <text class="product-name">{{item.name}}</text>
              <text class="product-desc">{{item.description}}</text>
              <view class="product-price-action">
                <text class="product-price">¥{{item.price}}</text>
                <view class="product-action">
                  <view class="action-minus" wx:if="{{item.count > 0}}" bindtap="minusDish" data-id="{{item.id}}">-</view>
                  <view class="action-count" wx:if="{{item.count > 0}}">{{item.count}}</view>
                  <view class="action-plus" bindtap="addDish" data-id="{{item.id}}">+</view>
                </view>
              </view>
            </view>
          </view>

          <view class="empty-products" wx:if="{{currentCategoryDishes.length === 0}}">
            <text class="empty-text">该分类暂无菜品</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 购物车 -->
    <view class="cart-container {{cartVisible ? 'cart-expanded' : ''}}">
      <!-- 添加遮罩层 -->
      <view class="cart-mask" wx:if="{{cartVisible}}" bindtap="toggleCart"></view>

      <view class="cart-header" bindtap="toggleCart">
        <view class="cart-left">
          <view class="cart-icon-container">
            <view class="cart-icon {{cartTotal.count > 0 ? 'active' : ''}}">
              <text class="cart-icon-text">🚚</text>
              <view class="cart-count" wx:if="{{cartTotal.count > 0}}">{{cartTotal.count}}</view>
            </view>
          </view>
          <view class="cart-info">
            <text class="cart-price" wx:if="{{cartTotal.count > 0}}">¥{{cartTotal.price}}</text>
            <text class="cart-empty" wx:else>购物车空空如也</text>
          </view>
        </view>
        <view class="cart-submit {{cartTotal.count > 0 ? 'active' : ''}}" bindtap="{{cartTotal.count > 0 ? 'goToCheckout' : ''}}">
          {{cartTotal.count > 0 ? '去结算' : '请选择服务'}}
        </view>
      </view>

      <!-- 购物车展开内容 -->
      <view class="cart-content" wx:if="{{cartVisible}}">
        <view class="cart-title">
          <text>已选商品</text>
          <text class="cart-clear" bindtap="clearCart">清空购物车</text>
        </view>
        <scroll-view scroll-y="true" class="cart-items">
          <view class="cart-item" wx:for="{{cartItems}}" wx:key="id">
            <text class="cart-item-name">{{item.name}}</text>
            <view class="cart-item-price-action">
              <text class="cart-item-price">¥{{item.price}}</text>
              <view class="product-action">
                <view class="action-minus" bindtap="minusDish" data-id="{{item.id}}">-</view>
                <view class="action-count">{{item.count}}</view>
                <view class="action-plus" bindtap="addDish" data-id="{{item.id}}">+</view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </block>

  <!-- 菜品详情弹窗 -->
  <view class="dish-detail-modal" wx:if="{{showDishModal}}">
    <view class="modal-mask" bindtap="hideDishDetail"></view>
    <view class="modal-content">
      <view class="modal-close" bindtap="hideDishDetail">×</view>
      <image class="modal-image" src="{{selectedDish.image || 'https://vegan.yiheship.com/static/images/default-dish.png'}}" mode="aspectFill"></image>
      <view class="modal-info">
        <view class="modal-name">{{selectedDish.name}}</view>
        <text class="modal-desc">{{selectedDish.description}}</text>
        <view class="modal-price-action">
          <text class="modal-price">¥{{selectedDish.price}}</text>
          <view class="product-action">
            <view class="action-minus" wx:if="{{selectedDish.count > 0}}" bindtap="minusDishInModal" data-id="{{selectedDish.id}}">-</view>
            <view class="action-count" wx:if="{{selectedDish.count > 0}}">{{selectedDish.count}}</view>
            <view class="action-plus" bindtap="addDishInModal" data-id="{{selectedDish.id}}">+</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 地址列表弹窗 -->
  <view class="address-modal" wx:if="{{showAddressList}}">
    <view class="modal-mask" bindtap="hideAddressSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>选择配送地址</text>
        <view class="modal-close" bindtap="hideAddressSelector">×</view>
      </view>
      <scroll-view scroll-y="true" class="address-list">
        <view class="address-item {{deliveryAddress && deliveryAddress.id === item.id ? 'selected' : ''}}"
              wx:for="{{savedAddresses}}"
              wx:key="id"
              bindtap="selectAddress"
              data-id="{{item.id}}">
          <view class="address-item-content">
            <view class="address-item-header">
              <text class="address-name">{{item.name}}</text>
              <text class="address-phone">{{item.phone}}</text>
              <view class="address-default" wx:if="{{item.isDefault}}">默认</view>
            </view>
            <text class="address-detail">{{item.province}}{{item.city}}{{item.district}}{{item.detail}}</text>
          </view>
          <view class="address-actions">
            <view class="action-btn edit" bindtap="showEditAddressForm" data-id="{{item.id}}" catchtap="true">编辑</view>
            <view class="action-btn delete" bindtap="deleteAddress" data-id="{{item.id}}" catchtap="true">删除</view>
            <view class="action-btn default" wx:if="{{!item.isDefault}}" bindtap="setDefaultAddress" data-id="{{item.id}}" catchtap="true">设为默认</view>
          </view>
        </view>

        <view class="empty-address" wx:if="{{savedAddresses.length === 0}}">
          <text class="empty-text">暂无保存的地址</text>
        </view>
      </scroll-view>

      <view class="address-footer">
        <view class="add-address-btn" bindtap="showAddAddressForm">
          <text class="add-icon">+</text>
          <text>新增地址</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 地址表单弹窗 -->
  <view class="address-form-modal" wx:if="{{showAddressForm}}">
    <view class="modal-mask" bindtap="hideAddressForm"></view>
    <view class="form-content">
      <view class="form-header">
        <text>{{editingAddress ? '编辑地址' : '新增地址'}}</text>
        <view class="modal-close" bindtap="hideAddressForm">×</view>
      </view>

      <scroll-view scroll-y="true" class="form-scroll">
        <view class="form-group">
          <text class="form-label">收货人</text>
          <input class="form-input"
                 placeholder="请输入收货人姓名"
                 value="{{addressForm.name}}"
                 data-field="name"
                 bindinput="onAddressFormInput" />
        </view>

        <view class="form-group">
          <text class="form-label">手机号码</text>
          <input class="form-input"
                 placeholder="请输入手机号码"
                 type="number"
                 value="{{addressForm.phone}}"
                 data-field="phone"
                 bindinput="onAddressFormInput" />
        </view>

        <view class="form-group">
          <view class="form-label-row">
            <text class="form-label">所在地区</text>
            <view class="location-btn" bindtap="getCurrentLocation">
              <text class="location-icon">📍</text>
              <text>获取当前位置</text>
            </view>
          </view>
          <picker mode="region"
                  value="{{[addressForm.province, addressForm.city, addressForm.district]}}"
                  bindchange="onRegionChange">
            <view class="picker-content">
              <text class="picker-text" wx:if="{{addressForm.province}}">
                {{addressForm.province}} {{addressForm.city}} {{addressForm.district}}
              </text>
              <text class="picker-placeholder" wx:else>请选择省市区</text>
              <text class="picker-arrow">></text>
            </view>
          </picker>
        </view>

        <view class="form-group">
          <text class="form-label">详细地址</text>
          <textarea class="form-textarea"
                    placeholder="请输入详细地址，如街道、楼牌号等"
                    value="{{addressForm.detail}}"
                    data-field="detail"
                    bindinput="onAddressFormInput"
                    maxlength="100" />
        </view>

        <view class="form-group checkbox-group">
          <label class="checkbox-label">
            <checkbox checked="{{addressForm.isDefault}}" bindchange="toggleDefaultAddress" />
            <text>设为默认地址</text>
          </label>
        </view>
      </scroll-view>

      <view class="form-footer">
        <view class="save-btn" bindtap="saveAddress">保存地址</view>
      </view>
    </view>
  </view>

  <!-- 时间选择弹窗 -->
  <view class="time-modal" wx:if="{{showTimeModal}}">
    <view class="modal-mask" bindtap="hideTimeSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>选择配送时间</text>
        <view class="modal-close" bindtap="hideTimeSelector">×</view>
      </view>
      <view class="time-slots">
        <view class="time-slot {{selectedTimeSlot && selectedTimeSlot.id === slot.id ? 'selected' : ''}}" 
              wx:for="{{timeSlots}}" 
              wx:key="id" 
              wx:for-item="slot"
              bindtap="selectTimeSlot" 
              data-slot="{{slot}}">
          <text>{{slot.label}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 结算页面 -->
  <view class="checkout-modal" wx:if="{{showCheckout}}">
    <view class="modal-mask" bindtap="hideCheckout"></view>
    <view class="checkout-content">
      <view class="checkout-header">
        <text class="checkout-title">订单确认</text>
        <view class="modal-close" bindtap="hideCheckout">×</view>
      </view>

      <scroll-view scroll-y="true" class="checkout-scroll">
        <!-- 配送信息 -->
        <view class="checkout-section">
          <text class="section-label">配送信息:</text>
          <view class="delivery-summary">
            <view class="summary-item">
              <text class="summary-label">配送地址:</text>
              <text class="summary-value">{{deliveryAddress.detail || '未选择'}}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">配送时间:</text>
              <text class="summary-value">{{selectedTimeSlot.label || '未选择'}}</text>
            </view>
          </view>
        </view>

        <!-- 商品列表 -->
        <view class="checkout-section">
          <text class="section-label">已选商品:</text>
          <scroll-view scroll-y="true" class="checkout-items">
            <block wx:if="{{cartItems && cartItems.length > 0}}">
              <view class="checkout-item" wx:for="{{cartItems}}" wx:key="id">
                <text class="checkout-item-name">{{item.name}}</text>
                <text class="checkout-item-count">x{{item.count}}</text>
                <text class="checkout-item-price">¥{{item.price * item.count}}</text>
              </view>
            </block>
            <view class="checkout-empty" wx:else>
              <text>购物车中暂无商品</text>
            </view>
          </scroll-view>
        </view>

        <!-- 优惠券选择 -->
        <view class="checkout-section">
          <text class="section-label">优惠券:</text>
          <view class="coupon-row" bindtap="showCouponList">
            <text class="coupon-label">选择优惠券</text>
            <view class="coupon-content">
              <text class="coupon-text" wx:if="{{selectedCoupons.length === 0}}">请选择优惠券</text>
              <text class="coupon-text selected" wx:else>已选择{{selectedCoupons.length}}张优惠券</text>
              <text class="coupon-arrow">></text>
            </view>
          </view>

          <!-- 显示已选择的优惠券详情 -->
          <view class="selected-coupons" wx:if="{{selectedCoupons.length > 0}}">
            <view class="coupon-item-detail" wx:for="{{selectedCoupons}}" wx:key="uniqueId">
              <view class="coupon-info">
                <text class="coupon-name">{{item.coupon.name}}</text>
                <text class="coupon-type">{{item.displayText}}</text>
              </view>
              <view class="coupon-actions">
                <text class="coupon-discount-amount">-¥{{item.discountAmount || 0}}</text>
              </view>
            </view>
          </view>

          <view class="coupon-discount" wx:if="{{couponDiscount > 0}}">
            <text class="discount-label">优惠券总折扣</text>
            <text class="discount-amount">-¥{{couponDiscount}}</text>
          </view>
        </view>

        <view class="checkout-total">
          <text>合计: ¥{{finalAmount || cartTotal.price}}</text>
        </view>
      </scroll-view>

      <view class="checkout-submit" bindtap="submitOrder">提交订单</view>
    </view>
  </view>

  <!-- 订单成功提示 -->
  <view class="order-success" wx:if="{{orderSuccess}}">
    <view class="success-icon">✓</view>
    <text class="success-text">配送订单提交成功！</text>
    <text class="success-order-id">订单号: {{orderId}}</text>
    <view class="success-actions">
      <view class="success-btn primary" bindtap="goToOrderDetail">查看订单</view>
    </view>
  </view>

  <!-- 优惠券列表组件 -->
  <coupon-list
    visible="{{showCouponList}}"
    order-amount="{{cartTotal.price}}"
    selected-coupon-ids="{{selectedCouponIds}}"
    allow-multiple="{{true}}"
    products="{{products}}"
    bind:close="hideCouponList"
    bind:select="onCouponSelect"
  ></coupon-list>
</view>
