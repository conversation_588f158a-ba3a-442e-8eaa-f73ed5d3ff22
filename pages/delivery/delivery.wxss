/* pages/delivery/delivery.wxss */

/* 总体主盒子 */
.page-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  color: #333;
  padding-bottom: 120rpx; /* 为底部购物车留出空间 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #fff;
  color: #939393;
}

.loading {
  width: 50rpx;
  height: 50rpx;
  border: 4rpx solid #ddd;
  border-top: 4rpx solid #ff4a4a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 配送信息区域 */
.delivery-info {
  background: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  margin: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  width: 160rpx;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-text {
  font-size: 28rpx;
  color: #333;
}

.info-placeholder {
  font-size: 28rpx;
  color: #999;
}

.info-arrow {
  color: #ccc;
  font-size: 24rpx;
}

/* 地址信息样式 */
.address-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.address-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.address-detail {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 主布局 - 左右分栏 */
.main-layout {
  display: flex;
  height: calc(100vh - 240rpx); /* 减去配送信息和底部购物车高度 */
}

/* 左侧边栏 */
.left-sidebar {
  width: 200rpx;
  background: #f5f5f5;
  flex-shrink: 0;
}

.categories-scroll {
  height: 100%;
}

.category-item {
  height: 40px;
  line-height: 40px;
  padding: 5px 35rpx;
  font-size: 26rpx;
  color: #333;
}

.category-item.active {
  background: #fff;
  color: #ff4a4a;
}

.category-text {
  font-size: 26rpx;
  text-align: center;
}

/* 右侧商品列表主盒子 */
.right-products {
  flex: 1;
  padding: 14rpx;
  box-sizing: border-box;
  background: #fff;
  padding-bottom: 180rpx;
}

.product-list {
  height: 100%;
  padding-bottom: 180rpx;
}

.product-category-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  padding: 20rpx 0 16rpx 0;
  margin-bottom: 8rpx;
}

/* 商品卡片 - 采用横向布局 */
.product-item {
  width: 100%;
  min-height: 200rpx;
  background-color: #fff;
  margin-bottom: 14rpx;
  display: flex;
  align-items: flex-start;
  padding: 10rpx;
  box-sizing: border-box;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 10rpx;
  object-fit: cover;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  margin-left: 15rpx;
  padding: 10rpx 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 160rpx;
  border-bottom: 1px solid #ddd;
}

.product-name {
  color: #000;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
}

.product-desc {
  font-size: 22rpx;
  color: #939393;
  margin-bottom: 8rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  flex-shrink: 0;
}

.product-price {
  color: #ff4a4a;
  font-size: 28rpx;
  font-weight: 700;
  flex: 1;
}

/* 商品操作按钮 - 简化样式 */
.product-action {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 4rpx;
  gap: 4rpx;
  border: 1px solid #ddd;
}

.action-minus,
.action-plus {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
}

.action-minus {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.action-plus {
  background: #ff4a4a;
  color: #fff;
  border: 1px solid #ff4a4a;
}

.action-count {
  width: 48rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
}

/* 购物车 */
.cart-container {
  position: fixed;
  bottom: 40rpx;
  left: 20rpx;
  right: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  z-index: 100;
  border: 1px solid #ddd;
}

.cart-expanded {
  z-index: 160;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 16rpx 16rpx 0 0;
}

.cart-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 150;
}

.cart-header {
  display: flex;
  height: 100rpx;
  align-items: center;
  padding: 0 30rpx;
  justify-content: space-between;
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  z-index: 170;
}

.cart-expanded .cart-header {
  border-radius: 16rpx 16rpx 0 0;
}

.cart-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.cart-icon-container {
  position: relative;
  margin-right: 20rpx;
}

.cart-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
}

.cart-icon.active {
  background: #ff4a4a;
  border-color: #ff4a4a;
}

.cart-icon-text {
  font-size: 28rpx;
  color: #939393;
}

.cart-icon.active .cart-icon-text {
  color: #fff;
}

.cart-count {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: #ff4757;
  color: #fff;
  font-size: 18rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
  font-weight: 600;
}

.cart-info {
  flex: 1;
}

.cart-price {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.cart-empty {
  font-size: 24rpx;
  color: #939393;
}

.cart-submit {
  width: 180rpx;
  height: 68rpx;
  border-radius: 34rpx;
  background: #f5f5f5;
  color: #939393;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #ddd;
}

.cart-submit.active {
  background: #ff4a4a;
  color: #fff;
  border-color: #ff4a4a;
}

/* 购物车展开内容 */
.cart-content {
  height: 400rpx;
  background: #fff;
  border-top: 1px solid #ddd;
  position: relative;
  z-index: 170;
}

.cart-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #ddd;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  background: #fff;
}

.cart-clear {
  color: #939393;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
  border: 1px solid #ddd;
}

.cart-items {
  height: 320rpx;
  padding: 0 16rpx;
  background: #fff;
  overflow-y: auto;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 18rpx 0;
  border-bottom: 1px solid #ddd;
  position: relative;
  min-height: 80rpx;
  width: 100%;
  box-sizing: border-box;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  max-width: 445rpx;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 16rpx;
  flex-shrink: 1;
}

.cart-item-price-action {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  width: 240rpx;
  justify-content: space-between;
  position: relative;
}

.cart-item-price {
  font-size: 24rpx;
  color: #ff4a4a;
  font-weight: 600;
  width: 80rpx;
  text-align: right;
  flex-shrink: 0;
}

/* 购物车中product-action的样式 */
.cart-item .product-action {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 4rpx;
  gap: 4rpx;
  border: 1px solid #ddd;
  width: 140rpx;
  flex-shrink: 0;
  justify-content: space-between;
}

.cart-item .action-minus,
.cart-item .action-plus {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  cursor: pointer;
  flex-shrink: 0;
}

.cart-item .action-minus {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cart-item .action-plus {
  background: #ff4a4a;
  color: #fff;
  border: 1px solid #ff4a4a;
}

.cart-item .action-count {
  width: 48rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  flex-shrink: 0;
}

/* 空状态 */
.empty-products {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #939393;
}

/* 菜品详情弹窗样式 */
.dish-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 200;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;
}

.modal-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  z-index: 10;
}

.modal-image {
  width: 100%;
  height: 360rpx;
  object-fit: cover;
}

.modal-info {
  padding: 28rpx;
  background: #fff;
}

.modal-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #333;
}

.modal-desc {
  font-size: 26rpx;
  color: #939393;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.modal-price-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1px solid #ddd;
}

.modal-price {
  font-size: 36rpx;
  color: #ff4a4a;
  font-weight: 600;
}

/* 时间选择弹窗 */
.time-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 300;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
}

.time-modal .modal-content {
  background: #fff;
  border-radius: 24rpx;
  width: 90%;
  max-height: 70%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: slide-up 0.3s ease;
  position: relative;
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #ff4a4a, #ff7676);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(255, 74, 74, 0.2);
  font-size: 32rpx;
  font-weight: 600;
}

.modal-header .modal-close {
  position: static;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-left: 20rpx;
}

/* 地址列表样式 */
.address-list {
  max-height: 600rpx;
  padding: 0 30rpx;
}

.address-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.address-item.selected {
  background: rgba(255, 74, 74, 0.05);
}

.address-item:last-child {
  border-bottom: none;
}

.address-item-content {
  flex: 1;
  margin-right: 20rpx;
}

.address-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  gap: 20rpx;
}

.address-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.address-phone {
  font-size: 26rpx;
  color: #666;
}

.address-default {
  background: #ff4a4a;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  line-height: 1;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.address-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  text-align: center;
  min-width: 60rpx;
}

.action-btn.edit {
  background: #4080ff;
  color: #fff;
}

.action-btn.delete {
  background: #ff6b6b;
  color: #fff;
}

.action-btn.default {
  background: #f5f5f5;
  color: #333;
  border: 1rpx solid #ddd;
}

.empty-address {
  padding: 80rpx 0;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.address-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.add-address-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  background: #ff4a4a;
  color: #fff;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 地址表单样式 */
.address-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 400;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
}

.form-content {
  background: #fff;
  border-radius: 24rpx;
  width: 90%;
  max-height: 85%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: slide-up 0.3s ease;
}

.form-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #ff4a4a, #ff7676);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(255, 74, 74, 0.2);
  font-size: 32rpx;
  font-weight: 600;
}

.form-header .modal-close {
  position: static;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-left: 20rpx;
}

.form-scroll {
  flex: 1;
  overflow-y: auto;
  padding: 30rpx;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.form-label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.location-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  color: #4080ff;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: 1rpx solid #4080ff;
}

.location-icon {
  font-size: 20rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #ff4a4a;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #ff4a4a;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  background: #fff;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-placeholder {
  font-size: 28rpx;
  color: #999;
}

.picker-arrow {
  color: #ccc;
  font-size: 24rpx;
}

.checkbox-group {
  margin-bottom: 20rpx;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.form-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.save-btn {
  width: 100%;
  padding: 28rpx;
  background: linear-gradient(135deg, #ff4a4a, #ff7676);
  color: #fff;
  border-radius: 12rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.save-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.time-slots {
  padding: 30rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.time-slot {
  padding: 30rpx 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  transition: all 0.3s ease;
}

.time-slot.selected {
  border-color: #ff4a4a;
  background: rgba(255, 74, 74, 0.1);
  color: #ff4a4a;
}

/* 结算弹窗样式 */
.checkout-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 300;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
}

.checkout-modal .modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 301;
}

.checkout-content {
  background: #fff;
  border-radius: 24rpx;
  width: 90%;
  max-height: 88%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: slide-up 0.3s ease;
  position: relative;
  z-index: 302;
}

.checkout-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #ff4a4a, #ff7676);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(255, 74, 74, 0.2);
  position: relative;
}

.checkout-title {
  font-size: 34rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  text-align: left;
}

.checkout-header .modal-close {
  position: static;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-left: 20rpx;
}

.checkout-scroll {
  flex: 1;
  overflow-y: auto;
  max-height: calc(85vh - 180rpx);
  padding: 10rpx 0;
  background: #fff;
}

/* 结算区块美化 */
.checkout-section {
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  background: #fff;
}

.section-label {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
  color: #333;
  width: 100%;
  padding: 20rpx 20rpx 0;
}

.delivery-summary {
  padding: 0 20rpx 20rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.summary-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.checkout-items {
  max-height: 320rpx;
  padding: 0 20rpx 20rpx;
  background: #fff;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

.checkout-items::-webkit-scrollbar {
  display: none;
}

.checkout-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  border-bottom: 1px solid #f0f0f0;
}

.checkout-item:last-child {
  border-bottom: none;
}

.checkout-item-name {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.checkout-item-count {
  margin: 0 16rpx;
  color: #666;
  font-weight: 500;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.checkout-item-price {
  color: #ff4a4a;
  font-weight: 600;
}

.checkout-empty {
  text-align: center;
  color: #999;
  padding: 40rpx 0;
}

/* 优惠券选择区域 */
.coupon-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx 16rpx;
}

.coupon-label {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.coupon-content {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.coupon-text {
  color: #999;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.coupon-text.selected {
  color: #4080ff;
}

.coupon-arrow {
  color: #ccc;
  font-size: 24rpx;
}

.coupon-discount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx 0;
  margin-bottom: 20rpx;
}

.discount-label {
  color: #666;
  font-size: 26rpx;
}

.discount-amount {
  color: #ff4444;
  font-size: 26rpx;
  font-weight: 500;
}

/* 已选择的优惠券详情 */
.selected-coupons {
  background: #f8f9fa;
  border-top: 1rpx solid #eee;
  margin: 0 -20rpx;
  padding: 0 20rpx;
}

.coupon-item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-item-detail:last-child {
  border-bottom: none;
}

.coupon-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.coupon-name {
  color: #333;
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.coupon-type {
  color: #666;
  font-size: 22rpx;
}

.coupon-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.coupon-discount-amount {
  color: #ff6b6b;
  font-size: 26rpx;
  font-weight: 600;
}

/* 总价美化 */
.checkout-total {
  margin: 20rpx 30rpx;
  padding: 20rpx;
  text-align: right;
  font-size: 34rpx;
  font-weight: 600;
  border-radius: 16rpx;
  background: #fff;
  color: #ff4a4a;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 提交按钮美化 */
.checkout-submit {
  height: 100rpx;
  margin-top: 20rpx;
  background: linear-gradient(135deg, #ff4a4a, #ff7676);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  border: none;
  letter-spacing: 4rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.checkout-submit:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.checkout-submit::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shine 2s infinite;
}

@keyframes shine {
  to {
    left: 100%;
  }
}

/* 订单成功提示 */
.order-success {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 400;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.success-icon {
  width: 100rpx;
  height: 100rpx;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #fff;
  margin-bottom: 24rpx;
}

.success-text {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #333;
}

.success-order-id {
  font-size: 24rpx;
  color: #939393;
  margin-bottom: 48rpx;
  background: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.success-actions {
  display: flex;
  gap: 16rpx;
}

.success-btn {
  width: 200rpx;
  height: 72rpx;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 500;
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.success-btn.primary {
  background: #ff4a4a;
  color: #fff;
  border-color: #ff4a4a;
}
