Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前选中的地址
    selectedAddress: {
      type: Object,
      value: null
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: '请选择配送地址'
    },
    // 是否显示地址选择器
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 地址管理相关
    savedAddresses: [],
    showAddressList: false,
    showAddressForm: false,
    editingAddress: null,
    addressForm: {
      id: null,
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 加载用户地址
     */
    loadUserAddress() {
      // 从本地存储获取保存的地址列表
      const savedAddresses = wx.getStorageSync('savedAddresses') || [];
      // 查找默认地址
      const defaultAddress = savedAddresses.find(addr => addr.isDefault) || savedAddresses[0] || null;

      this.setData({
        savedAddresses
      });

      // 如果没有选中地址且有默认地址，则设置默认地址
      if (!this.data.selectedAddress && defaultAddress) {
        this.setData({
          selectedAddress: defaultAddress
        });
        // 通知父组件地址变化
        this.triggerEvent('addresschange', defaultAddress);
      }
    },

    /**
     * 保存地址到本地存储
     */
    saveAddressesToStorage() {
      wx.setStorageSync('savedAddresses', this.data.savedAddresses);
    },

    /**
     * 显示地址选择
     */
    showAddressSelector() {
      this.setData({
        showAddressList: true
      });
    },

    /**
     * 隐藏地址选择
     */
    hideAddressSelector() {
      this.setData({
        showAddressList: false,
        showAddressForm: false
      });
    },

    /**
     * 选择地址
     */
    selectAddress(e) {
      const addressId = e.currentTarget.dataset.id;
      const selectedAddress = this.data.savedAddresses.find(addr => addr.id === addressId);

      if (selectedAddress) {
        this.setData({
          selectedAddress: selectedAddress,
          showAddressList: false
        });
        // 通知父组件地址变化
        this.triggerEvent('addresschange', selectedAddress);
      }
    },

    /**
     * 显示新增地址表单
     */
    showAddAddressForm() {
      this.setData({
        showAddressForm: true,
        editingAddress: null,
        addressForm: {
          id: null,
          name: '',
          phone: '',
          province: '',
          city: '',
          district: '',
          detail: '',
          isDefault: this.data.savedAddresses.length === 0 // 如果是第一个地址，默认设为默认地址
        }
      });
    },

    /**
     * 获取当前位置
     */
    getCurrentLocation() {
      wx.showLoading({ title: '获取位置中...' });

      // 先检查位置权限
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation']) {
            // 已授权，直接获取位置
            this.getLocationData();
          } else {
            // 未授权，请求授权
            wx.authorize({
              scope: 'scope.userLocation',
              success: () => {
                // 授权成功，获取位置
                this.getLocationData();
              },
              fail: () => {
                wx.hideLoading();
                wx.showModal({
                  title: '位置权限',
                  content: '需要获取您的位置信息来自动填充地址，请在设置中开启位置权限',
                  showCancel: true,
                  confirmText: '去设置',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting();
                    }
                  }
                });
              }
            });
          }
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({
            title: '获取权限信息失败',
            icon: 'none'
          });
        }
      });
    },

    /**
     * 获取位置数据
     */
    getLocationData() {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          // 调用腾讯地图逆地址解析API
          this.reverseGeocode(res.latitude, res.longitude);
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('获取位置失败:', err);
          wx.showToast({
            title: '获取位置失败，请检查定位权限',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    /**
     * 逆地址解析
     */
    reverseGeocode(lat, lng) {
      const key = 'CHMBZ-6DYKL-JW6PE-EJPKN-4POB7-VYF23'; // 腾讯地图API密钥
      
      wx.request({
        url: 'https://apis.map.qq.com/ws/geocoder/v1/',
        data: {
          location: `${lat},${lng}`,
          key: key,
          get_poi: 1
        },
        success: (res) => {
          wx.hideLoading();

          if (res.data.status === 0) {
            const result = res.data.result;
            const addressComponent = result.address_component;

            this.setData({
              'addressForm.province': addressComponent.province,
              'addressForm.city': addressComponent.city,
              'addressForm.district': addressComponent.district,
              'addressForm.detail': result.formatted_addresses?.recommend || result.address
            });

            wx.showToast({
              title: '位置获取成功',
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '地址解析失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('逆地址解析失败:', err);
          wx.showToast({
            title: '地址解析失败',
            icon: 'none'
          });
        }
      });
    },

    /**
     * 显示编辑地址表单
     */
    showEditAddressForm(e) {
      const addressId = e.currentTarget.dataset.id;
      const address = this.data.savedAddresses.find(addr => addr.id === addressId);

      if (address) {
        this.setData({
          showAddressForm: true,
          editingAddress: address,
          addressForm: { ...address }
        });
      }
    },

    /**
     * 隐藏地址表单
     */
    hideAddressForm() {
      this.setData({
        showAddressForm: false,
        editingAddress: null
      });
    },

    /**
     * 表单输入处理
     */
    onAddressFormInput(e) {
      const { field } = e.currentTarget.dataset;
      const { value } = e.detail;

      this.setData({
        [`addressForm.${field}`]: value
      });
    },

    /**
     * 切换默认地址
     */
    toggleDefaultAddress(e) {
      const isDefault = e.detail.value;
      this.setData({
        'addressForm.isDefault': isDefault
      });
    },

    /**
     * 选择地区
     */
    onRegionChange(e) {
      const [province, city, district] = e.detail.value;
      this.setData({
        'addressForm.province': province,
        'addressForm.city': city,
        'addressForm.district': district
      });
    },

    /**
     * 保存地址
     */
    saveAddress() {
      const { addressForm, savedAddresses, editingAddress } = this.data;

      // 表单验证
      if (!addressForm.name.trim()) {
        wx.showToast({ title: '请输入收货人姓名', icon: 'none' });
        return;
      }

      if (!addressForm.phone.trim()) {
        wx.showToast({ title: '请输入手机号码', icon: 'none' });
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(addressForm.phone)) {
        wx.showToast({ title: '请输入正确的手机号码', icon: 'none' });
        return;
      }

      if (!addressForm.province || !addressForm.city || !addressForm.district) {
        wx.showToast({ title: '请选择所在地区', icon: 'none' });
        return;
      }

      if (!addressForm.detail.trim()) {
        wx.showToast({ title: '请输入详细地址', icon: 'none' });
        return;
      }

      let newAddresses = [...savedAddresses];

      // 如果设置为默认地址，先取消其他地址的默认状态
      if (addressForm.isDefault) {
        newAddresses = newAddresses.map(addr => ({ ...addr, isDefault: false }));
      }

      if (editingAddress) {
        // 编辑现有地址
        const index = newAddresses.findIndex(addr => addr.id === editingAddress.id);
        if (index !== -1) {
          newAddresses[index] = { ...addressForm };
        }
      } else {
        // 新增地址
        const newAddress = {
          ...addressForm,
          id: Date.now().toString() // 简单的ID生成
        };
        newAddresses.push(newAddress);
      }

      this.setData({
        savedAddresses: newAddresses,
        showAddressForm: false,
        editingAddress: null
      });

      // 保存到本地存储
      this.saveAddressesToStorage();

      // 如果是默认地址或者是第一个地址，设置为当前选中地址
      if (addressForm.isDefault || newAddresses.length === 1) {
        const savedAddress = editingAddress ?
          newAddresses.find(addr => addr.id === editingAddress.id) :
          newAddresses[newAddresses.length - 1];

        this.setData({
          selectedAddress: savedAddress,
          showAddressList: false
        });
        // 通知父组件地址变化
        this.triggerEvent('addresschange', savedAddress);
      }

      wx.showToast({
        title: editingAddress ? '地址修改成功' : '地址添加成功',
        icon: 'success'
      });
    },

    /**
     * 删除地址
     */
    deleteAddress(e) {
      const addressId = e.currentTarget.dataset.id;
      const address = this.data.savedAddresses.find(addr => addr.id === addressId);

      if (!address) return;

      wx.showModal({
        title: '确认删除',
        content: `确定要删除地址"${address.name} ${address.detail}"吗？`,
        success: (res) => {
          if (res.confirm) {
            let newAddresses = this.data.savedAddresses.filter(addr => addr.id !== addressId);

            // 如果删除的是当前选中的地址，重新选择默认地址
            let newSelectedAddress = this.data.selectedAddress;
            if (this.data.selectedAddress && this.data.selectedAddress.id === addressId) {
              newSelectedAddress = newAddresses.find(addr => addr.isDefault) || newAddresses[0] || null;
            }

            this.setData({
              savedAddresses: newAddresses,
              selectedAddress: newSelectedAddress
            });

            // 保存到本地存储
            this.saveAddressesToStorage();

            // 通知父组件地址变化
            this.triggerEvent('addresschange', newSelectedAddress);

            wx.showToast({
              title: '地址删除成功',
              icon: 'success'
            });
          }
        }
      });
    },

    /**
     * 设置默认地址
     */
    setDefaultAddress(e) {
      const addressId = e.currentTarget.dataset.id;

      const newAddresses = this.data.savedAddresses.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      }));

      const defaultAddress = newAddresses.find(addr => addr.id === addressId);

      this.setData({
        savedAddresses: newAddresses,
        selectedAddress: defaultAddress,
        showAddressList: false
      });

      // 保存到本地存储
      this.saveAddressesToStorage();

      // 通知父组件地址变化
      this.triggerEvent('addresschange', defaultAddress);

      wx.showToast({
        title: '默认地址设置成功',
        icon: 'success'
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放到页面节点树后执行
      this.loadUserAddress();
    }
  }
});
